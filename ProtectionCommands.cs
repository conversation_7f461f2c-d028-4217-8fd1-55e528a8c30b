using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace EntityProtection
{
    public class ProtectionCommands
    {
        /// <summary>
        /// 保护选定的图元
        /// </summary>
        [CommandMethod("PROTECT")]
        public void ProtectEntities()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                // 提示用户选择要保护的图元
                PromptSelectionOptions pso = new PromptSelectionOptions();
                pso.MessageForAdding = "\n选择要保护的图元: ";
                pso.AllowDuplicates = false;

                PromptSelectionResult psr = ed.GetSelection(pso);
                if (psr.Status != PromptStatus.OK) return;

                int protectedCount = 0;
                int alreadyProtectedCount = 0;

                foreach (ObjectId objId in psr.Value.GetObjectIds())
                {
                    if (EntityProtectionManager.IsEntityProtected(objId))
                    {
                        alreadyProtectedCount++;
                        continue;
                    }

                    EntityProtectionManager.ProtectEntity(objId);
                    protectedCount++;
                }

                ed.WriteMessage($"\n成功保护 {protectedCount} 个图元");
                if (alreadyProtectedCount > 0)
                {
                    ed.WriteMessage($"\n{alreadyProtectedCount} 个图元已经受保护");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n保护图元时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消保护选定的图元
        /// </summary>
        [CommandMethod("UNPROTECT")]
        public void UnprotectEntities()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                // 提示用户选择要取消保护的图元
                PromptSelectionOptions pso = new PromptSelectionOptions();
                pso.MessageForAdding = "\n选择要取消保护的图元: ";
                pso.AllowDuplicates = false;

                PromptSelectionResult psr = ed.GetSelection(pso);
                if (psr.Status != PromptStatus.OK) return;

                int unprotectedCount = 0;
                int notProtectedCount = 0;

                foreach (ObjectId objId in psr.Value.GetObjectIds())
                {
                    if (!EntityProtectionManager.IsEntityProtected(objId))
                    {
                        notProtectedCount++;
                        continue;
                    }

                    EntityProtectionManager.UnprotectEntity(objId);
                    unprotectedCount++;
                }

                ed.WriteMessage($"\n成功取消保护 {unprotectedCount} 个图元");
                if (notProtectedCount > 0)
                {
                    ed.WriteMessage($"\n{notProtectedCount} 个图元本来就没有保护");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n取消保护图元时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查选定图元的保护状态
        /// </summary>
        [CommandMethod("CHECKPROTECTION")]
        public void CheckProtectionStatus()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                // 提示用户选择要检查的图元
                PromptSelectionOptions pso = new PromptSelectionOptions();
                pso.MessageForAdding = "\n选择要检查保护状态的图元: ";
                pso.AllowDuplicates = false;

                PromptSelectionResult psr = ed.GetSelection(pso);
                if (psr.Status != PromptStatus.OK) return;

                int protectedCount = 0;
                int unprotectedCount = 0;

                foreach (ObjectId objId in psr.Value.GetObjectIds())
                {
                    bool isProtected = EntityProtectionManager.IsEntityProtected(objId);
                    if (isProtected)
                    {
                        protectedCount++;
                        ed.WriteMessage($"\n图元 {objId.Handle} 受保护");
                    }
                    else
                    {
                        unprotectedCount++;
                        ed.WriteMessage($"\n图元 {objId.Handle} 未受保护");
                    }
                }

                ed.WriteMessage($"\n\n总计: {protectedCount} 个受保护, {unprotectedCount} 个未受保护");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n检查保护状态时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试保护功能 - 尝试修改受保护的图元
        /// </summary>
        [CommandMethod("TESTPROTECTION")]
        public void TestProtection()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                // 提示用户选择要测试的图元
                PromptEntityOptions peo = new PromptEntityOptions("\n选择一个图元进行保护测试: ");
                PromptEntityResult per = ed.GetEntity(peo);
                if (per.Status != PromptStatus.OK) return;

                ObjectId entityId = per.ObjectId;

                // 检查是否受保护
                bool isProtected = EntityProtectionManager.IsEntityProtected(entityId);
                ed.WriteMessage($"\n图元保护状态: {(isProtected ? "受保护" : "未受保护")}");

                if (!isProtected)
                {
                    ed.WriteMessage("\n图元未受保护，先添加保护...");
                    EntityProtectionManager.ProtectEntity(entityId);
                    ed.WriteMessage("\n保护已添加");
                }

                // 尝试修改图元
                ed.WriteMessage("\n正在尝试修改受保护的图元...");
                
                using (Transaction tr = doc.TransactionManager.StartTransaction())
                {
                    try
                    {
                        Entity entity = tr.GetObject(entityId, OpenMode.ForWrite) as Entity;
                        if (entity != null)
                        {
                            // 尝试修改图元的颜色
                            entity.ColorIndex = 1; // 红色
                            tr.Commit();
                            ed.WriteMessage("\n警告: 修改成功！保护可能没有正常工作。");
                        }
                    }
                    catch (Autodesk.AutoCAD.Runtime.Exception acEx)
                    {
                        ed.WriteMessage($"\n保护生效: {acEx.Message}");
                        tr.Abort();
                    }
                    catch (System.Exception ex)
                    {
                        ed.WriteMessage($"\n修改失败: {ex.Message}");
                        tr.Abort();
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n测试保护功能时出错: {ex.Message}");
            }
        }
    }
}
