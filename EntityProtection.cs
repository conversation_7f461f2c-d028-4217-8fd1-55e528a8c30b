using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using System;
using System.Collections.Generic;

namespace EntityProtection
{
    /// <summary>
    /// 图元保护管理器 - 使用扩展字典和Persistent Reactors防止图元被编辑
    /// </summary>
    public static class EntityProtectionManager
    {
        #region 常量定义
        private const string PROTECTION_DICT_NAME = "ENTITY_PROTECTION";
        private const string PROTECTION_KEY = "PROTECTED";
        private const string REACTOR_KEY = "PROTECTION_REACTOR";
        private const string PROTECTION_LEVEL_KEY = "PROTECTION_LEVEL";
        private const string PROTECTION_TIME_KEY = "PROTECTION_TIME";
        private const string PROTECTION_USER_KEY = "PROTECTION_USER";
        #endregion

        #region 保护级别枚举
        /// <summary>
        /// 保护级别
        /// </summary>
        public enum ProtectionLevel
        {
            /// <summary>完全保护 - 禁止所有修改和删除</summary>
            Full,
            /// <summary>只读保护 - 允许查看，禁止修改</summary>
            ReadOnly,
            /// <summary>部分保护 - 允许移动，禁止删除和其他修改</summary>
            Partial,
            /// <summary>删除保护 - 只禁止删除</summary>
            DeleteOnly
        }
        #endregion

        #region 公共方法

        /// <summary>
        /// 为指定图元添加保护
        /// </summary>
        /// <param name="entityId">图元ID</param>
        /// <param name="level">保护级别，默认为完全保护</param>
        /// <param name="userName">设置保护的用户名，默认为当前用户</param>
        /// <returns>是否成功添加保护</returns>
        public static bool ProtectEntity(ObjectId entityId, ProtectionLevel level = ProtectionLevel.Full, string userName = null)
        {
            if (entityId.IsNull || entityId.IsErased) return false;

            Database db = entityId.Database ?? HostApplicationServices.WorkingDatabase;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    if (!(tr.GetObject(entityId, OpenMode.ForWrite) is Entity entity))
                        return false;

                    // 检查是否已经受保护
                    if (IsEntityProtected(entityId))
                    {
                        // 更新保护级别
                        return UpdateProtectionLevel(entityId, level, userName);
                    }

                    // 获取或创建扩展字典
                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull)
                    {
                        entity.CreateExtensionDictionary();
                        extDictId = entity.ExtensionDictionary;
                    }

                    if (!(tr.GetObject(extDictId, OpenMode.ForWrite) is DBDictionary extDict))
                        return false;

                    // 创建保护字典
                    DBDictionary protectionDict = GetOrCreateProtectionDict(extDict, tr);
                    if (protectionDict == null) return false;

                    // 添加保护信息
                    AddProtectionData(protectionDict, tr, level, userName ?? Environment.UserName);

                    // 创建并附加Persistent Reactor
                    if (!AttachProtectionReactor(entity, protectionDict, tr, db))
                        return false;

                    tr.Commit();
                    return true;
                }
                catch (System.Exception ex)
                {
                    LogError($"保护实体时出错: {ex.Message}");
                    tr.Abort();
                    return false;
                }
            }
        }

        /// <summary>
        /// 批量保护多个图元
        /// </summary>
        /// <param name="entityIds">图元ID集合</param>
        /// <param name="level">保护级别</param>
        /// <param name="userName">用户名</param>
        /// <returns>成功保护的图元数量</returns>
        public static int ProtectEntities(IEnumerable<ObjectId> entityIds, ProtectionLevel level = ProtectionLevel.Full, string userName = null)
        {
            int successCount = 0;
            foreach (ObjectId entityId in entityIds)
            {
                if (ProtectEntity(entityId, level, userName))
                    successCount++;
            }
            return successCount;
        }

        /// <summary>
        /// 移除图元保护
        /// </summary>
        /// <param name="entityId">图元ID</param>
        /// <returns>是否成功移除保护</returns>
        public static bool UnprotectEntity(ObjectId entityId)
        {
            if (entityId.IsNull || entityId.IsErased) return false;

            Database db = entityId.Database ?? HostApplicationServices.WorkingDatabase;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    if (!(tr.GetObject(entityId, OpenMode.ForWrite) is Entity entity))
                        return false;

                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull) return false;

                    if (!(tr.GetObject(extDictId, OpenMode.ForRead) is DBDictionary extDict) ||
                        !extDict.Contains(PROTECTION_DICT_NAME))
                        return false;

                    if (!(tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForRead) is DBDictionary protectionDict))
                        return false;

                    // 移除reactor
                    RemoveProtectionReactor(entity, protectionDict, tr);

                    // 删除保护字典
                    extDict.UpgradeOpen();
                    extDict.Remove(PROTECTION_DICT_NAME);

                    tr.Commit();
                    return true;
                }
                catch (System.Exception ex)
                {
                    LogError($"移除保护时出错: {ex.Message}");
                    tr.Abort();
                    return false;
                }
            }
        }

        /// <summary>
        /// 批量移除多个图元的保护
        /// </summary>
        /// <param name="entityIds">图元ID集合</param>
        /// <returns>成功移除保护的图元数量</returns>
        public static int UnprotectEntities(IEnumerable<ObjectId> entityIds)
        {
            int successCount = 0;
            foreach (ObjectId entityId in entityIds)
            {
                if (UnprotectEntity(entityId))
                    successCount++;
            }
            return successCount;
        }

        /// <summary>
        /// 检查图元是否受保护
        /// </summary>
        /// <param name="entityId">图元ID</param>
        /// <returns>是否受保护</returns>
        public static bool IsEntityProtected(ObjectId entityId)
        {
            if (entityId.IsNull || entityId.IsErased) return false;

            Database db = entityId.Database ?? HostApplicationServices.WorkingDatabase;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    if (!(tr.GetObject(entityId, OpenMode.ForRead) is Entity entity))
                        return false;

                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull) return false;

                    if (!(tr.GetObject(extDictId, OpenMode.ForRead) is DBDictionary extDict) ||
                        !extDict.Contains(PROTECTION_DICT_NAME))
                        return false;

                    if (!(tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForRead) is DBDictionary protectionDict))
                        return false;

                    return protectionDict.Contains(PROTECTION_KEY);
                }
                catch
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 获取图元的保护信息
        /// </summary>
        /// <param name="entityId">图元ID</param>
        /// <returns>保护信息，如果未受保护则返回null</returns>
        public static ProtectionInfo GetProtectionInfo(ObjectId entityId)
        {
            if (entityId.IsNull || entityId.IsErased) return null;

            Database db = entityId.Database ?? HostApplicationServices.WorkingDatabase;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    if (!(tr.GetObject(entityId, OpenMode.ForRead) is Entity entity))
                        return null;

                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull) return null;

                    if (!(tr.GetObject(extDictId, OpenMode.ForRead) is DBDictionary extDict) ||
                        !extDict.Contains(PROTECTION_DICT_NAME))
                        return null;

                    if (!(tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForRead) is DBDictionary protectionDict))
                        return null;

                    return ExtractProtectionInfo(protectionDict, tr);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 更新图元的保护级别
        /// </summary>
        /// <param name="entityId">图元ID</param>
        /// <param name="newLevel">新的保护级别</param>
        /// <param name="userName">用户名</param>
        /// <returns>是否成功更新</returns>
        public static bool UpdateProtectionLevel(ObjectId entityId, ProtectionLevel newLevel, string userName = null)
        {
            if (!IsEntityProtected(entityId)) return false;

            // 先移除保护，再重新添加
            if (UnprotectEntity(entityId))
            {
                return ProtectEntity(entityId, newLevel, userName);
            }
            return false;
        }

        /// <summary>
        /// 获取所有受保护的图元
        /// </summary>
        /// <param name="database">数据库，为null时使用当前工作数据库</param>
        /// <returns>受保护的图元ID列表</returns>
        public static List<ObjectId> GetAllProtectedEntities(Database database = null)
        {
            var protectedEntities = new List<ObjectId>();
            database = database ?? HostApplicationServices.WorkingDatabase;

            using (Transaction tr = database.TransactionManager.StartTransaction())
            {
                try
                {
                    // 遍历模型空间
                    BlockTable bt = tr.GetObject(database.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord modelSpace = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                    foreach (ObjectId objId in modelSpace)
                    {
                        if (IsEntityProtected(objId))
                        {
                            protectedEntities.Add(objId);
                        }
                    }

                    // 遍历图纸空间
                    BlockTableRecord paperSpace = tr.GetObject(bt[BlockTableRecord.PaperSpace], OpenMode.ForRead) as BlockTableRecord;
                    foreach (ObjectId objId in paperSpace)
                    {
                        if (IsEntityProtected(objId))
                        {
                            protectedEntities.Add(objId);
                        }
                    }

                    tr.Commit();
                }
                catch
                {
                    tr.Abort();
                }
            }

            return protectedEntities;
        }

        #endregion

        #region 数据结构

        /// <summary>
        /// 保护信息类
        /// </summary>
        public class ProtectionInfo
        {
            /// <summary>保护级别</summary>
            public ProtectionLevel Level { get; set; }

            /// <summary>保护时间</summary>
            public DateTime ProtectionTime { get; set; }

            /// <summary>设置保护的用户</summary>
            public string UserName { get; set; }

            /// <summary>Reactor对象ID</summary>
            public ObjectId ReactorId { get; set; }

            public ProtectionInfo()
            {
                Level = ProtectionLevel.Full;
                ProtectionTime = DateTime.Now;
                UserName = Environment.UserName;
                ReactorId = ObjectId.Null;
            }

            public override string ToString()
            {
                return $"保护级别: {Level}, 保护时间: {ProtectionTime:yyyy-MM-dd HH:mm:ss}, 用户: {UserName}";
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 获取或创建保护字典
        /// </summary>
        private static DBDictionary GetOrCreateProtectionDict(DBDictionary extDict, Transaction tr)
        {
            try
            {
                if (extDict.Contains(PROTECTION_DICT_NAME))
                {
                    return tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForWrite) as DBDictionary;
                }
                else
                {
                    var protectionDict = new DBDictionary();
                    extDict.SetAt(PROTECTION_DICT_NAME, protectionDict);
                    tr.AddNewlyCreatedDBObject(protectionDict, true);
                    return protectionDict;
                }
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 添加保护数据到字典
        /// </summary>
        private static void AddProtectionData(DBDictionary protectionDict, Transaction tr, ProtectionLevel level, string userName)
        {
            // 保护标记记录
            var protectionRecord = new Xrecord
            {
                Data = new ResultBuffer(
                    new TypedValue((int)DxfCode.Text, "PROTECTED")
                )
            };
            protectionDict.SetAt(PROTECTION_KEY, protectionRecord);
            tr.AddNewlyCreatedDBObject(protectionRecord, true);

            // 保护级别记录
            var levelRecord = new Xrecord
            {
                Data = new ResultBuffer(
                    new TypedValue((int)DxfCode.Text, level.ToString())
                )
            };
            protectionDict.SetAt(PROTECTION_LEVEL_KEY, levelRecord);
            tr.AddNewlyCreatedDBObject(levelRecord, true);

            // 保护时间记录
            var timeRecord = new Xrecord
            {
                Data = new ResultBuffer(
                    new TypedValue((int)DxfCode.Text, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                )
            };
            protectionDict.SetAt(PROTECTION_TIME_KEY, timeRecord);
            tr.AddNewlyCreatedDBObject(timeRecord, true);

            // 用户记录
            var userRecord = new Xrecord
            {
                Data = new ResultBuffer(
                    new TypedValue((int)DxfCode.Text, userName)
                )
            };
            protectionDict.SetAt(PROTECTION_USER_KEY, userRecord);
            tr.AddNewlyCreatedDBObject(userRecord, true);
        }

        /// <summary>
        /// 附加保护反应器
        /// </summary>
        private static bool AttachProtectionReactor(Entity entity, DBDictionary protectionDict, Transaction tr, Database db)
        {
            try
            {
                // 创建并附加Persistent Reactor
                var reactor = new EntityProtectionReactor();
                ObjectId reactorId = db.AddDBObject(reactor);

                // 将reactor附加到实体
                entity.AddPersistentReactor(reactorId);

                // 在保护字典中记录reactor ID
                var reactorRecord = new Xrecord
                {
                    Data = new ResultBuffer(
                        new TypedValue((int)DxfCode.SoftPointerId, reactorId)
                    )
                };
                protectionDict.SetAt(REACTOR_KEY, reactorRecord);
                tr.AddNewlyCreatedDBObject(reactorRecord, true);

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 移除保护反应器
        /// </summary>
        private static void RemoveProtectionReactor(Entity entity, DBDictionary protectionDict, Transaction tr)
        {
            try
            {
                if (protectionDict.Contains(REACTOR_KEY))
                {
                    if (tr.GetObject(protectionDict.GetAt(REACTOR_KEY), OpenMode.ForRead) is Xrecord reactorRecord)
                    {
                        foreach (TypedValue tv in reactorRecord.Data)
                        {
                            if (tv.TypeCode == (int)DxfCode.SoftPointerId && tv.Value is ObjectId reactorId)
                            {
                                entity.RemovePersistentReactor(reactorId);

                                // 删除reactor对象
                                if (tr.GetObject(reactorId, OpenMode.ForWrite) is DBObject reactorObj)
                                {
                                    reactorObj.Erase();
                                }
                                break;
                            }
                        }
                    }
                }
            }
            catch
            {
                // 忽略错误
            }
        }

        /// <summary>
        /// 从保护字典中提取保护信息
        /// </summary>
        private static ProtectionInfo ExtractProtectionInfo(DBDictionary protectionDict, Transaction tr)
        {
            var info = new ProtectionInfo();

            try
            {
                // 读取保护级别
                if (protectionDict.Contains(PROTECTION_LEVEL_KEY))
                {
                    if (tr.GetObject(protectionDict.GetAt(PROTECTION_LEVEL_KEY), OpenMode.ForRead) is Xrecord levelRecord)
                    {
                        foreach (TypedValue tv in levelRecord.Data)
                        {
                            if (tv.TypeCode == (int)DxfCode.Text &&
                                Enum.TryParse<ProtectionLevel>(tv.Value.ToString(), out ProtectionLevel level))
                            {
                                info.Level = level;
                                break;
                            }
                        }
                    }
                }

                // 读取保护时间
                if (protectionDict.Contains(PROTECTION_TIME_KEY))
                {
                    if (tr.GetObject(protectionDict.GetAt(PROTECTION_TIME_KEY), OpenMode.ForRead) is Xrecord timeRecord)
                    {
                        foreach (TypedValue tv in timeRecord.Data)
                        {
                            if (tv.TypeCode == (int)DxfCode.Text &&
                                DateTime.TryParse(tv.Value.ToString(), out DateTime time))
                            {
                                info.ProtectionTime = time;
                                break;
                            }
                        }
                    }
                }

                // 读取用户名
                if (protectionDict.Contains(PROTECTION_USER_KEY))
                {
                    if (tr.GetObject(protectionDict.GetAt(PROTECTION_USER_KEY), OpenMode.ForRead) is Xrecord userRecord)
                    {
                        foreach (TypedValue tv in userRecord.Data)
                        {
                            if (tv.TypeCode == (int)DxfCode.Text)
                            {
                                info.UserName = tv.Value.ToString();
                                break;
                            }
                        }
                    }
                }

                // 读取Reactor ID
                if (protectionDict.Contains(REACTOR_KEY))
                {
                    if (tr.GetObject(protectionDict.GetAt(REACTOR_KEY), OpenMode.ForRead) is Xrecord reactorRecord)
                    {
                        foreach (TypedValue tv in reactorRecord.Data)
                        {
                            if (tv.TypeCode == (int)DxfCode.SoftPointerId && tv.Value is ObjectId reactorId)
                            {
                                info.ReactorId = reactorId;
                                break;
                            }
                        }
                    }
                }
            }
            catch
            {
                // 返回默认信息
            }

            return info;
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        private static void LogError(string message)
        {
            try
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage($"\n{message}");
            }
            catch
            {
                // 忽略日志错误
            }
        }

        #endregion
    }
}
