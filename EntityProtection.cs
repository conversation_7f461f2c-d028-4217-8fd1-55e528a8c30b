using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using System;

namespace EntityProtection
{
    public class EntityProtectionManager
    {
        private const string PROTECTION_DICT_NAME = "ENTITY_PROTECTION";
        private const string PROTECTION_KEY = "PROTECTED";
        private const string REACTOR_KEY = "PROTECTION_REACTOR";

        /// <summary>
        /// 为指定图元添加保护
        /// </summary>
        /// <param name="entityId">图元ID</param>
        /// <param name="protectionLevel">保护级别</param>
        public static void ProtectEntity(ObjectId entityId, string protectionLevel = "FULL")
        {
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    Entity entity = tr.GetObject(entityId, OpenMode.ForWrite) as Entity;
                    if (entity == null) return;

                    // 获取或创建扩展字典
                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull)
                    {
                        entity.CreateExtensionDictionary();
                        extDictId = entity.ExtensionDictionary;
                    }

                    DBDictionary extDict = tr.GetObject(extDictId, OpenMode.ForWrite) as DBDictionary;

                    // 创建保护字典
                    DBDictionary protectionDict;
                    if (extDict.Contains(PROTECTION_DICT_NAME))
                    {
                        protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForWrite) as DBDictionary;
                    }
                    else
                    {
                        protectionDict = new DBDictionary();
                        extDict.SetAt(PROTECTION_DICT_NAME, protectionDict);
                        tr.AddNewlyCreatedDBObject(protectionDict, true);
                    }

                    // 添加保护标记
                    Xrecord protectionRecord = new Xrecord();
                    protectionRecord.Data = new ResultBuffer(
                        new TypedValue((int)DxfCode.Text, protectionLevel),
                        new TypedValue((int)DxfCode.Text, DateTime.Now.ToString())
                    );

                    protectionDict.SetAt(PROTECTION_KEY, protectionRecord);
                    tr.AddNewlyCreatedDBObject(protectionRecord, true);

                    // 创建并附加Persistent Reactor
                    EntityProtectionReactor reactor = new EntityProtectionReactor();
                    ObjectId reactorId = db.AddDBObject(reactor);
                    
                    // 将reactor附加到实体
                    entity.AddPersistentReactor(reactorId);

                    // 在保护字典中记录reactor ID
                    Xrecord reactorRecord = new Xrecord();
                    reactorRecord.Data = new ResultBuffer(
                        new TypedValue((int)DxfCode.SoftPointerId, reactorId)
                    );
                    protectionDict.SetAt(REACTOR_KEY, reactorRecord);
                    tr.AddNewlyCreatedDBObject(reactorRecord, true);

                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\n保护实体时出错: {ex.Message}");
                    tr.Abort();
                }
            }
        }

        /// <summary>
        /// 移除图元保护
        /// </summary>
        /// <param name="entityId">图元ID</param>
        public static void UnprotectEntity(ObjectId entityId)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    Entity entity = tr.GetObject(entityId, OpenMode.ForWrite) as Entity;
                    if (entity == null) return;

                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull) return;

                    DBDictionary extDict = tr.GetObject(extDictId, OpenMode.ForRead) as DBDictionary;
                    if (!extDict.Contains(PROTECTION_DICT_NAME)) return;

                    DBDictionary protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForRead) as DBDictionary;

                    // 移除reactor
                    if (protectionDict.Contains(REACTOR_KEY))
                    {
                        Xrecord reactorRecord = tr.GetObject(protectionDict.GetAt(REACTOR_KEY), OpenMode.ForRead) as Xrecord;
                        foreach (TypedValue tv in reactorRecord.Data)
                        {
                            if (tv.TypeCode == (int)DxfCode.SoftPointerId)
                            {
                                ObjectId reactorId = (ObjectId)tv.Value;
                                entity.RemovePersistentReactor(reactorId);
                                
                                // 删除reactor对象
                                DBObject reactorObj = tr.GetObject(reactorId, OpenMode.ForWrite);
                                reactorObj.Erase();
                                break;
                            }
                        }
                    }

                    // 删除保护字典
                    extDict.UpgradeOpen();
                    extDict.Remove(PROTECTION_DICT_NAME);

                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\n移除保护时出错: {ex.Message}");
                    tr.Abort();
                }
            }
        }

        /// <summary>
        /// 检查图元是否受保护
        /// </summary>
        /// <param name="entityId">图元ID</param>
        /// <returns>是否受保护</returns>
        public static bool IsEntityProtected(ObjectId entityId)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    Entity entity = tr.GetObject(entityId, OpenMode.ForRead) as Entity;
                    if (entity == null) return false;

                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull) return false;

                    DBDictionary extDict = tr.GetObject(extDictId, OpenMode.ForRead) as DBDictionary;
                    if (!extDict.Contains(PROTECTION_DICT_NAME)) return false;

                    DBDictionary protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForRead) as DBDictionary;
                    return protectionDict.Contains(PROTECTION_KEY);
                }
                catch
                {
                    return false;
                }
            }
        }
    }
}
