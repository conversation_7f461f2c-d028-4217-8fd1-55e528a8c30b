# EntityProtectionManager 使用示例

这是一个完整的AutoCAD.NET图元保护管理器类，您可以直接在项目中使用。

## 快速开始

### 1. 添加引用
确保您的项目引用了以下AutoCAD.NET程序集：
- AcCoreMgd.dll
- AcDbMgd.dll  
- AcMgd.dll

### 2. 复制类文件
将 `EntityProtectionManager.cs` 文件复制到您的项目中。

### 3. 基本使用

```csharp
using EntityProtection;
using Autodesk.AutoCAD.DatabaseServices;

// 保护单个图元
ObjectId entityId = GetSomeEntityId(); // 您的图元ID
bool success = EntityProtectionManager.ProtectEntity(entityId);

// 检查图元是否受保护
bool isProtected = EntityProtectionManager.IsEntityProtected(entityId);

// 移除保护
bool removed = EntityProtectionManager.UnprotectEntity(entityId);
```

## 详细API说明

### 保护图元

```csharp
// 基本保护（完全保护）
EntityProtectionManager.ProtectEntity(entityId);

// 指定保护级别
EntityProtectionManager.ProtectEntity(entityId, EntityProtectionManager.ProtectionLevel.ReadOnly);

// 指定保护级别和用户名
EntityProtectionManager.ProtectEntity(entityId, EntityProtectionManager.ProtectionLevel.Full, "张三");

// 批量保护
List<ObjectId> entityIds = GetEntityIds();
int protectedCount = EntityProtectionManager.ProtectEntities(entityIds);
```

### 保护级别说明

```csharp
public enum ProtectionLevel
{
    Full,       // 完全保护 - 禁止所有修改和删除
    ReadOnly,   // 只读保护 - 允许查看，禁止修改  
    Partial,    // 部分保护 - 允许移动，禁止删除和其他修改
    DeleteOnly  // 删除保护 - 只禁止删除
}
```

### 检查和获取保护信息

```csharp
// 检查是否受保护
if (EntityProtectionManager.IsEntityProtected(entityId))
{
    // 图元受保护
}

// 获取详细保护信息
var info = EntityProtectionManager.GetProtectionInfo(entityId);
if (info != null)
{
    Console.WriteLine($"保护级别: {info.Level}");
    Console.WriteLine($"保护时间: {info.ProtectionTime}");
    Console.WriteLine($"保护用户: {info.UserName}");
}
```

### 移除保护

```csharp
// 移除单个图元保护
bool success = EntityProtectionManager.UnprotectEntity(entityId);

// 批量移除保护
List<ObjectId> entityIds = GetProtectedEntityIds();
int unprotectedCount = EntityProtectionManager.UnprotectEntities(entityIds);
```

### 更新保护级别

```csharp
// 更新现有保护的级别
bool updated = EntityProtectionManager.UpdateProtectionLevel(
    entityId, 
    EntityProtectionManager.ProtectionLevel.ReadOnly,
    "李四"
);
```

### 获取所有受保护的图元

```csharp
// 获取当前数据库中所有受保护的图元
List<ObjectId> protectedEntities = EntityProtectionManager.GetAllProtectedEntities();

// 获取指定数据库中的受保护图元
Database db = GetSomeDatabase();
List<ObjectId> protectedEntities2 = EntityProtectionManager.GetAllProtectedEntities(db);
```

## 完整使用示例

```csharp
[CommandMethod("DEMO_PROTECT")]
public void DemoProtection()
{
    Document doc = Application.DocumentManager.MdiActiveDocument;
    Editor ed = doc.Editor;
    
    try
    {
        // 1. 选择要保护的图元
        PromptEntityOptions peo = new PromptEntityOptions("\n选择要保护的图元: ");
        PromptEntityResult per = ed.GetEntity(peo);
        if (per.Status != PromptStatus.OK) return;
        
        ObjectId entityId = per.ObjectId;
        
        // 2. 添加保护
        bool protected = EntityProtectionManager.ProtectEntity(
            entityId, 
            EntityProtectionManager.ProtectionLevel.Full,
            "演示用户"
        );
        
        if (protected)
        {
            ed.WriteMessage("\n图元保护成功！");
            
            // 3. 显示保护信息
            var info = EntityProtectionManager.GetProtectionInfo(entityId);
            if (info != null)
            {
                ed.WriteMessage($"\n{info}");
            }
            
            // 4. 测试保护效果
            ed.WriteMessage("\n请尝试修改或删除该图元，应该会被阻止。");
            
            // 5. 稍后可以移除保护
            // EntityProtectionManager.UnprotectEntity(entityId);
        }
        else
        {
            ed.WriteMessage("\n保护图元失败！");
        }
    }
    catch (System.Exception ex)
    {
        ed.WriteMessage($"\n操作出错: {ex.Message}");
    }
}
```

## 注意事项

### 1. 性能考虑
- Reactor会在每次图元修改时触发，大量受保护图元可能影响性能
- 建议只对重要图元使用保护功能

### 2. 持久化
- 保护信息存储在图元的扩展字典中，随DWG文件保存
- 在没有加载此保护系统的AutoCAD环境中，保护不会生效

### 3. 兼容性
- 需要AutoCAD 2018或更高版本
- 需要.NET Framework 4.8或更高版本

### 4. 错误处理
- 所有公共方法都有完善的错误处理
- 失败时会在AutoCAD命令行显示错误信息

## 扩展开发

如果您需要自定义保护逻辑，可以：

1. **修改保护级别枚举**：添加新的保护类型
2. **扩展ProtectionInfo类**：添加更多保护属性
3. **自定义Reactor行为**：修改EntityProtectionReactor类的逻辑
4. **添加事件通知**：在保护/取消保护时触发自定义事件

## 技术原理

1. **扩展字典**：使用AutoCAD的扩展字典功能存储保护信息
2. **Persistent Reactors**：使用持久化反应器监听图元修改事件
3. **事件拦截**：在Modified和Erased事件中检查保护状态并阻止操作

这个设计确保了保护信息的持久化存储和实时的保护效果。
