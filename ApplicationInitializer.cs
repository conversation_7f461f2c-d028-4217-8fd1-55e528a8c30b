using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Runtime;
using System;

namespace EntityProtection
{
    /// <summary>
    /// 应用程序初始化类 - 注册自定义类和事件
    /// </summary>
    public class ApplicationInitializer : IExtensionApplication
    {
        /// <summary>
        /// 应用程序初始化
        /// </summary>
        public void Initialize()
        {
            try
            {
                // 注册自定义DBObject类
                RegisterCustomClasses();

                // 订阅文档事件
                Application.DocumentManager.DocumentCreated += OnDocumentCreated;
                Application.DocumentManager.DocumentToBeDestroyed += OnDocumentToBeDestroyed;

                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                    "\n实体保护系统已加载。\n可用命令: PROTECT, UNPROTECT, CHECKPROTECTION, TESTPROTECTION"
                );
            }
            catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                    $"\n初始化实体保护系统时出错: {ex.Message}"
                );
            }
        }

        /// <summary>
        /// 应用程序终止
        /// </summary>
        public void Terminate()
        {
            try
            {
                // 取消订阅事件
                Application.DocumentManager.DocumentCreated -= OnDocumentCreated;
                Application.DocumentManager.DocumentToBeDestroyed -= OnDocumentToBeDestroyed;

                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                    "\n实体保护系统已卸载。"
                );
            }
            catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                    $"\n卸载实体保护系统时出错: {ex.Message}"
                );
            }
        }

        /// <summary>
        /// 注册自定义类
        /// </summary>
        private void RegisterCustomClasses()
        {
            // 注册EntityProtectionReactor类
            RXClass rxClass = RXClass.GetClass(typeof(EntityProtectionReactor));
            if (rxClass == null)
            {
                // 如果类未注册，则注册它
                rxClass = RXClass.GetClass(typeof(DBObject));
                RXClass.CreateClass(
                    typeof(EntityProtectionReactor),
                    rxClass,
                    AppDomain.CurrentDomain.GetAssemblies()[0].GetName().Name,
                    "EntityProtectionReactor",
                    "EntityProtection.EntityProtectionReactor",
                    DxfName.Parse("ENTITY_PROTECTION_REACTOR"),
                    0
                );
            }
        }

        /// <summary>
        /// 文档创建事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnDocumentCreated(object sender, DocumentCollectionEventArgs e)
        {
            try
            {
                // 可以在这里为新文档设置特定的保护规则
                e.Document.Editor.WriteMessage("\n实体保护系统已为新文档激活。");
            }
            catch (System.Exception ex)
            {
                e.Document.Editor.WriteMessage($"\n为新文档激活保护系统时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 文档销毁事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnDocumentToBeDestroyed(object sender, DocumentCollectionEventArgs e)
        {
            try
            {
                // 清理文档相关的保护资源
                // 这里可以添加清理代码
            }
            catch (System.Exception ex)
            {
                // 记录错误但不阻止文档关闭
                System.Diagnostics.Debug.WriteLine($"清理文档保护资源时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 全局事件处理器 - 处理数据库级别的事件
    /// </summary>
    public static class GlobalEventHandler
    {
        private static bool _eventsRegistered = false;

        /// <summary>
        /// 注册全局事件
        /// </summary>
        public static void RegisterEvents()
        {
            if (_eventsRegistered) return;

            try
            {
                // 注册数据库事件
                Database.ObjectModified += OnObjectModified;
                Database.ObjectErased += OnObjectErased;

                _eventsRegistered = true;
            }
            catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                    $"\n注册全局事件时出错: {ex.Message}"
                );
            }
        }

        /// <summary>
        /// 取消注册全局事件
        /// </summary>
        public static void UnregisterEvents()
        {
            if (!_eventsRegistered) return;

            try
            {
                Database.ObjectModified -= OnObjectModified;
                Database.ObjectErased -= OnObjectErased;

                _eventsRegistered = false;
            }
            catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                    $"\n取消注册全局事件时出错: {ex.Message}"
                );
            }
        }

        /// <summary>
        /// 对象修改事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private static void OnObjectModified(object sender, ObjectEventArgs e)
        {
            try
            {
                // 这里可以添加额外的全局保护逻辑
                // 注意：这个事件在修改后触发，主要用于日志记录
                if (EntityProtectionManager.IsEntityProtected(e.DBObject.ObjectId))
                {
                    Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                        $"\n警告: 受保护的图元 {e.DBObject.ObjectId.Handle} 被修改了！"
                    );
                }
            }
            catch
            {
                // 忽略错误，避免影响正常操作
            }
        }

        /// <summary>
        /// 对象删除事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private static void OnObjectErased(object sender, ObjectErasedEventArgs e)
        {
            try
            {
                // 记录受保护图元的删除
                if (e.Erased && EntityProtectionManager.IsEntityProtected(e.DBObject.ObjectId))
                {
                    Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                        $"\n警告: 受保护的图元 {e.DBObject.ObjectId.Handle} 被删除了！"
                    );
                }
            }
            catch
            {
                // 忽略错误，避免影响正常操作
            }
        }
    }
}
