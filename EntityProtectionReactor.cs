using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Runtime;
using System;

namespace EntityProtection
{
    /// <summary>
    /// 实体保护反应器 - 防止受保护的实体被修改
    /// 这是一个简化版本，专门用于EntityProtectionManager
    /// </summary>
    public class EntityProtectionReactor : DBObject
    {
        private const string PROTECTION_DICT_NAME = "ENTITY_PROTECTION";
        private const string PROTECTION_KEY = "PROTECTED";

        public EntityProtectionReactor() : base()
        {
        }

        /// <summary>
        /// 当实体即将被修改时触发
        /// </summary>
        /// <param name="dbObj">被修改的数据库对象</param>
        public override void Modified(DBObject dbObj)
        {
            try
            {
                // 检查是否为实体对象
                Entity entity = dbObj as Entity;
                if (entity == null) return;

                // 检查实体是否受保护
                if (IsEntityProtected(entity))
                {
                    // 阻止修改 - 抛出异常
                    throw new Autodesk.AutoCAD.Runtime.Exception(
                        ErrorStatus.CannotChangeActiveViewport,
                        "此图元受到保护，无法修改！"
                    );
                }
            }
            catch (System.Exception ex)
            {
                // 记录错误但不阻止操作
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                    $"\n保护检查时出错: {ex.Message}"
                );
            }

            base.Modified(dbObj);
        }

        /// <summary>
        /// 当实体即将被删除时触发
        /// </summary>
        /// <param name="dbObj">被删除的数据库对象</param>
        public override void Erased(DBObject dbObj, bool erasing)
        {
            try
            {
                if (erasing) // 只在删除时检查，不在恢复时检查
                {
                    Entity entity = dbObj as Entity;
                    if (entity != null && IsEntityProtected(entity))
                    {
                        throw new Autodesk.AutoCAD.Runtime.Exception(
                            ErrorStatus.CannotChangeActiveViewport,
                            "此图元受到保护，无法删除！"
                        );
                    }
                }
            }
            catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage(
                    $"\n删除保护检查时出错: {ex.Message}"
                );
            }

            base.Erased(dbObj, erasing);
        }

        /// <summary>
        /// 检查实体是否受保护
        /// </summary>
        /// <param name="entity">要检查的实体</param>
        /// <returns>是否受保护</returns>
        private bool IsEntityProtected(Entity entity)
        {
            try
            {
                ObjectId extDictId = entity.ExtensionDictionary;
                if (extDictId.IsNull) return false;

                using (Transaction tr = entity.Database.TransactionManager.StartTransaction())
                {
                    DBDictionary extDict = tr.GetObject(extDictId, OpenMode.ForRead) as DBDictionary;
                    if (extDict == null || !extDict.Contains(PROTECTION_DICT_NAME))
                        return false;

                    DBDictionary protectionDict = tr.GetObject(
                        extDict.GetAt(PROTECTION_DICT_NAME), 
                        OpenMode.ForRead
                    ) as DBDictionary;

                    bool isProtected = protectionDict != null && protectionDict.Contains(PROTECTION_KEY);
                    tr.Commit();
                    return isProtected;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 读取DWG文件时的反序列化
        /// </summary>
        /// <param name="filer">文件读取器</param>
        /// <returns>错误状态</returns>
        public override ErrorStatus DwgInFields(DwgFiler filer)
        {
            try
            {
                ErrorStatus es = base.DwgInFields(filer);
                if (es != ErrorStatus.OK) return es;

                // 这里可以读取自定义数据
                // 例如: int version = filer.ReadInt32();

                return ErrorStatus.OK;
            }
            catch
            {
                return ErrorStatus.FilerError;
            }
        }

        /// <summary>
        /// 保存到DWG文件时的序列化
        /// </summary>
        /// <param name="filer">文件写入器</param>
        /// <returns>错误状态</returns>
        public override ErrorStatus DwgOutFields(DwgFiler filer)
        {
            try
            {
                ErrorStatus es = base.DwgOutFields(filer);
                if (es != ErrorStatus.OK) return es;

                // 这里可以写入自定义数据
                // 例如: filer.WriteInt32(1); // version

                return ErrorStatus.OK;
            }
            catch
            {
                return ErrorStatus.FilerError;
            }
        }

        /// <summary>
        /// 读取DXF文件时的反序列化
        /// </summary>
        /// <param name="filer">DXF读取器</param>
        /// <returns>错误状态</returns>
        public override ErrorStatus DxfInFields(DxfFiler filer)
        {
            try
            {
                ErrorStatus es = base.DxfInFields(filer);
                if (es != ErrorStatus.OK) return es;

                // DXF格式的自定义数据读取
                return ErrorStatus.OK;
            }
            catch
            {
                return ErrorStatus.FilerError;
            }
        }

        /// <summary>
        /// 保存到DXF文件时的序列化
        /// </summary>
        /// <param name="filer">DXF写入器</param>
        /// <returns>错误状态</returns>
        public override ErrorStatus DxfOutFields(DxfFiler filer)
        {
            try
            {
                ErrorStatus es = base.DxfOutFields(filer);
                if (es != ErrorStatus.OK) return es;

                // DXF格式的自定义数据写入
                return ErrorStatus.OK;
            }
            catch
            {
                return ErrorStatus.FilerError;
            }
        }
    }
}
