using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Runtime;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GMDI_ForCAD.App.Utils.反应器
{
    /// <summary>
    /// 图元保护管理器 - 使用扩展字典防止图元被编辑。
    /// 这是一个静态类，提供了所有保护相关的功能。
    /// </summary>
    public static class EntityProtectionManager
    {
        #region 常量、枚举和数据结构
        private const string PROTECTION_DICT_NAME = "ENTITY_PROTECTION";
        private const string PROTECTION_KEY = "PROTECTED";
        private const string PROTECTION_LEVEL_KEY = "PROTECTION_LEVEL";
        private const string PROTECTION_TIME_KEY = "PROTECTION_TIME";
        private const string PROTECTION_USER_KEY = "PROTECTION_USER";

        public enum ProtectionLevel
        {
            Full, ReadOnly, Partial, DeleteOnly
        }

        public class ProtectionInfo
        {
            public ProtectionLevel Level { get; set; }
            public DateTime ProtectionTime { get; set; }
            public string UserName { get; set; }

            public ProtectionInfo()
            {
                Level = ProtectionLevel.Full;
                ProtectionTime = DateTime.Now;
                UserName = Environment.UserName;
            }

            public override string ToString()
            {
                return $"保护级别: {Level}, 保护时间: {ProtectionTime:yyyy-MM-dd HH:mm:ss}, 用户: {UserName}";
            }
        }
        #endregion

        #region 全局反应器管理 (修正版)
        private static bool _reactorsAttached = false;

        public static void AttachGlobalReactors()
        {
            if (_reactorsAttached) return;

            try
            {
                // 使用实际存在的事件
                Database.ObjectModified += OnObjectModified;
                Database.ObjectErased += OnObjectErased;
                
                // 订阅文档事件以便为新文档附加反应器
                Application.DocumentManager.DocumentActivated += OnDocumentActivated;
                
                _reactorsAttached = true;
                LogError("保护反应器已附加");
            }
            catch (System.Exception ex)
            {
                LogError($"附加反应器失败: {ex.Message}");
            }
        }

        public static void DetachGlobalReactors()
        {
            if (!_reactorsAttached) return;

            try
            {
                Database.ObjectModified -= OnObjectModified;
                Database.ObjectErased -= OnObjectErased;
                Application.DocumentManager.DocumentActivated -= OnDocumentActivated;
                
                _reactorsAttached = false;
                LogError("保护反应器已分离");
            }
            catch (System.Exception ex)
            {
                LogError($"分离反应器失败: {ex.Message}");
            }
        }

        private static void OnDocumentActivated(object sender, DocumentCollectionEventArgs e)
        {
            // 为新激活的文档附加数据库事件
            if (e.Document?.Database != null)
            {
                e.Document.Database.ObjectModified += OnObjectModified;
                e.Document.Database.ObjectErased += OnObjectErased;
            }
        }

        private static void OnObjectModified(object sender, ObjectEventArgs e)
        {
            try
            {
                if (IsEntityProtected(e.DBObject.ObjectId))
                {
                    var info = GetProtectionInfo(e.DBObject.ObjectId);
                    LogError($"警告: 受保护的图元 {e.DBObject.ObjectId.Handle} 被修改了！{info}");
                }
            }
            catch
            {
                // 忽略错误，避免影响正常操作
            }
        }

        private static void OnObjectErased(object sender, ObjectErasedEventArgs e)
        {
            try
            {
                if (e.Erased && IsEntityProtected(e.DBObject.ObjectId))
                {
                    var info = GetProtectionInfo(e.DBObject.ObjectId);
                    LogError($"警告: 受保护的图元 {e.DBObject.ObjectId.Handle} 被删除了！{info}");
                }
            }
            catch
            {
                // 忽略错误，避免影响正常操作
            }
        }

        /// <summary>
        /// 验证图元操作（用于在自定义命令中主动检查）
        /// </summary>
        /// <param name="entityId">图元ID</param>
        /// <param name="operation">操作类型</param>
        /// <returns>是否允许操作</returns>
        public static bool ValidateEntityOperation(ObjectId entityId, string operation = "修改")
        {
            if (IsEntityProtected(entityId))
            {
                var info = GetProtectionInfo(entityId);
                LogError($"操作被阻止: 图元受到保护，无法{operation}。{info}");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 验证选择集操作
        /// </summary>
        /// <param name="entityIds">图元ID集合</param>
        /// <param name="operation">操作类型</param>
        /// <returns>是否允许操作</returns>
        public static bool ValidateSelectionOperation(IEnumerable<ObjectId> entityIds, string operation = "修改")
        {
            var protectedEntities = entityIds.Where(IsEntityProtected).ToList();
            
            if (protectedEntities.Any())
            {
                LogError($"操作被阻止: 选择集中包含 {protectedEntities.Count} 个受保护的图元，无法{operation}。");
                foreach (var objId in protectedEntities)
                {
                    LogError($"  - 受保护图元: {objId.Handle}");
                }
                return false;
            }
            return true;
        }

        #endregion

        #region 公共方法

        public static bool ProtectEntity(ObjectId entityId, ProtectionLevel level = ProtectionLevel.Full, string userName = null)
        {
            if (entityId.IsNull || entityId.IsErased) return false;
            Database db = entityId.Database ?? HostApplicationServices.WorkingDatabase;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    if (!(tr.GetObject(entityId, OpenMode.ForWrite) is Entity entity)) return false;
                    if (IsEntityProtected(entityId)) return UpdateProtectionLevel(entityId, level, userName);

                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull)
                    {
                        entity.CreateExtensionDictionary();
                        extDictId = entity.ExtensionDictionary;
                    }
                    if (!(tr.GetObject(extDictId, OpenMode.ForWrite) is DBDictionary extDict)) return false;

                    DBDictionary protectionDict = GetOrCreateProtectionDict(extDict, tr);
                    if (protectionDict == null) return false;

                    AddProtectionData(protectionDict, tr, level, userName ?? Environment.UserName);
                    tr.Commit();
                    return true;
                }
                catch (System.Exception ex) 
                { 
                    LogError($"保护实体时出错: {ex.Message}"); 
                    tr.Abort(); 
                    return false; 
                }
            }
        }

        public static int ProtectEntities(IEnumerable<ObjectId> entityIds, ProtectionLevel level = ProtectionLevel.Full, string userName = null)
        {
            return entityIds.Count(entityId => ProtectEntity(entityId, level, userName));
        }

        public static bool UnprotectEntity(ObjectId entityId)
        {
            if (entityId.IsNull || entityId.IsErased) return false;
            Database db = entityId.Database ?? HostApplicationServices.WorkingDatabase;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    if (!(tr.GetObject(entityId, OpenMode.ForWrite) is Entity entity)) return false;
                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull) return true;

                    if (!(tr.GetObject(extDictId, OpenMode.ForWrite) is DBDictionary extDict) || 
                        !extDict.Contains(PROTECTION_DICT_NAME)) return true;

                    extDict.Remove(PROTECTION_DICT_NAME);
                    tr.Commit();
                    return true;
                }
                catch (System.Exception ex) 
                { 
                    LogError($"移除保护时出错: {ex.Message}"); 
                    tr.Abort(); 
                    return false; 
                }
            }
        }

        public static int UnprotectEntities(IEnumerable<ObjectId> entityIds)
        {
            return entityIds.Count(UnprotectEntity);
        }

        public static bool UpdateProtectionLevel(ObjectId entityId, ProtectionLevel newLevel, string userName = null)
        {
            if (!IsEntityProtected(entityId)) return false;
            
            Database db = entityId.Database ?? HostApplicationServices.WorkingDatabase;
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var entity = tr.GetObject(entityId, OpenMode.ForRead);
                    var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
                    var protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForWrite) as DBDictionary;

                    // 移除旧记录
                    if (protectionDict.Contains(PROTECTION_LEVEL_KEY)) protectionDict.Remove(PROTECTION_LEVEL_KEY);
                    if (protectionDict.Contains(PROTECTION_TIME_KEY)) protectionDict.Remove(PROTECTION_TIME_KEY);
                    if (protectionDict.Contains(PROTECTION_USER_KEY)) protectionDict.Remove(PROTECTION_USER_KEY);

                    // 添加新记录 (不添加主保护键)
                    AddProtectionData(protectionDict, tr, newLevel, userName ?? Environment.UserName, false);
                    tr.Commit();
                    return true;
                }
                catch (System.Exception ex)
                {
                    LogError($"更新保护级别时出错: {ex.Message}");
                    tr.Abort();
                    return false;
                }
            }
        }

        public static bool IsEntityProtected(ObjectId entityId)
        {
            if (entityId.IsNull || entityId.IsErased) return false;
            
            Database db = entityId.Database ?? HostApplicationServices.WorkingDatabase;
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var entity = tr.GetObject(entityId, OpenMode.ForRead);
                    if (entity == null) return false;
                    ObjectId extDictId = entity.ExtensionDictionary;
                    if (extDictId.IsNull) return false;
                    var extDict = tr.GetObject(extDictId, OpenMode.ForRead) as DBDictionary;
                    return extDict?.Contains(PROTECTION_DICT_NAME) ?? false;
                }
                catch { return false; }
            }
        }

        public static ProtectionInfo GetProtectionInfo(ObjectId entityId)
        {
            if (!IsEntityProtected(entityId)) return null;
            
            Database db = entityId.Database ?? HostApplicationServices.WorkingDatabase;
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var entity = tr.GetObject(entityId, OpenMode.ForRead);
                    var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
                    var protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForRead) as DBDictionary;
                    return ExtractProtectionInfo(protectionDict, tr);
                }
                catch { return null; }
            }
        }

        #endregion

        #region 私有辅助方法

        private static DBDictionary GetOrCreateProtectionDict(DBDictionary extDict, Transaction tr)
        {
            if (extDict.Contains(PROTECTION_DICT_NAME))
                return tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForWrite) as DBDictionary;
            else
            {
                var protectionDict = new DBDictionary();
                extDict.SetAt(PROTECTION_DICT_NAME, protectionDict);
                tr.AddNewlyCreatedDBObject(protectionDict, true);
                return protectionDict;
            }
        }

        private static void AddProtectionData(DBDictionary dict, Transaction tr, ProtectionLevel level, string user, bool addMainKey = true)
        {
            if (addMainKey) dict.SetAt(PROTECTION_KEY, CreateXrecord("PROTECTED", tr));
            dict.SetAt(PROTECTION_LEVEL_KEY, CreateXrecord(level.ToString(), tr));
            dict.SetAt(PROTECTION_TIME_KEY, CreateXrecord(DateTime.Now.ToString("o"), tr)); // 使用 "o" 格式确保可往返
            dict.SetAt(PROTECTION_USER_KEY, CreateXrecord(user, tr));
        }

        private static Xrecord CreateXrecord(string value, Transaction tr)
        {
            var xrec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, value)) };
            tr.AddNewlyCreatedDBObject(xrec, true);
            return xrec;
        }

        private static ProtectionInfo ExtractProtectionInfo(DBDictionary protectionDict, Transaction tr)
        {
            var info = new ProtectionInfo();
            try
            {
                string levelStr = ReadXrecordValue(protectionDict.GetAt(PROTECTION_LEVEL_KEY), tr);
                if (Enum.TryParse(levelStr, out ProtectionLevel level))
                    info.Level = level;
            }
            catch { }

            try
            {
                string timeStr = ReadXrecordValue(protectionDict.GetAt(PROTECTION_TIME_KEY), tr);
                if (DateTime.TryParse(timeStr, out DateTime time))
                    info.ProtectionTime = time;
            }
            catch { }

            try
            {
                info.UserName = ReadXrecordValue(protectionDict.GetAt(PROTECTION_USER_KEY), tr);
            }
            catch { }

            return info;
        }

        private static string ReadXrecordValue(ObjectId xrecordId, Transaction tr)
        {
            if (xrecordId.IsNull) return string.Empty;
            var xrec = tr.GetObject(xrecordId, OpenMode.ForRead) as Xrecord;
            if (xrec?.Data == null) return string.Empty;

            foreach (TypedValue tv in xrec.Data)
            {
                if (tv.TypeCode == (int)DxfCode.Text)
                    return tv.Value?.ToString() ?? string.Empty;
            }
            return string.Empty;
        }

        private static void LogError(string message)
        {
            try
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage($"\n[保护系统] {message}");
            }
            catch
            {
                // 忽略日志错误
            }
        }

        #endregion
    }
}
