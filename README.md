# AutoCAD.NET 实体保护系统

这个项目演示了如何在AutoCAD.NET中使用扩展字典属性配合Persistent Reactors来防止图元被编辑。

## 功能特性

- ✅ 使用扩展字典存储保护标记
- ✅ 使用Persistent Reactors监听图元修改事件
- ✅ 防止受保护图元被修改或删除
- ✅ 保护信息持久化存储（随图纸保存）
- ✅ 提供完整的命令接口

## 技术原理

### 1. 扩展字典 (Extension Dictionary)
- 每个AutoCAD图元都可以有一个扩展字典
- 扩展字典可以存储自定义数据
- 数据随图纸一起保存，实现持久化

### 2. Persistent Reactors
- 继承自DBObject的自定义类
- 可以附加到图元上监听事件
- 在图元被修改时触发回调函数
- 支持DWG/DXF文件的序列化和反序列化

### 3. 保护机制
```
图元 → 扩展字典 → 保护字典 → {
    PROTECTED: 保护级别和时间戳
    PROTECTION_REACTOR: Reactor对象ID
}
```

## 可用命令

### PROTECT
保护选定的图元，防止被修改或删除。

```
命令: PROTECT
选择要保护的图元: [选择图元]
成功保护 X 个图元
```

### UNPROTECT
取消选定图元的保护。

```
命令: UNPROTECT
选择要取消保护的图元: [选择图元]
成功取消保护 X 个图元
```

### CHECKPROTECTION
检查选定图元的保护状态。

```
命令: CHECKPROTECTION
选择要检查保护状态的图元: [选择图元]
图元 ABC123 受保护
图元 DEF456 未受保护
```

### TESTPROTECTION
测试保护功能是否正常工作。

```
命令: TESTPROTECTION
选择一个图元进行保护测试: [选择图元]
图元保护状态: 受保护
正在尝试修改受保护的图元...
保护生效: 此图元受到保护，无法修改！
```

## 编译和部署

### 1. 编译要求
- Visual Studio 2019 或更高版本
- .NET Framework 4.8
- AutoCAD 2024 或兼容版本

### 2. 引用设置
确保项目引用了正确的AutoCAD.NET程序集：
- AcCoreMgd.dll
- AcDbMgd.dll
- AcMgd.dll

### 3. 编译步骤
```bash
# 使用Visual Studio编译
Build → Build Solution

# 或使用MSBuild
msbuild EntityProtection.csproj /p:Configuration=Release
```

### 4. 部署
1. 将编译生成的 `EntityProtection.dll` 复制到AutoCAD可访问的位置
2. 在AutoCAD中使用 `NETLOAD` 命令加载程序集
3. 或者将DLL放在AutoCAD的自动加载目录中

## 使用示例

### 基本使用流程

1. **加载插件**
   ```
   命令: NETLOAD
   选择文件: EntityProtection.dll
   实体保护系统已加载。
   可用命令: PROTECT, UNPROTECT, CHECKPROTECTION, TESTPROTECTION
   ```

2. **保护图元**
   ```
   命令: PROTECT
   选择要保护的图元: [选择几个图元]
   成功保护 3 个图元
   ```

3. **测试保护**
   - 尝试移动、删除或修改受保护的图元
   - 系统会阻止操作并显示错误消息

4. **取消保护**
   ```
   命令: UNPROTECT
   选择要取消保护的图元: [选择受保护的图元]
   成功取消保护 3 个图元
   ```

## 高级功能

### 自定义保护级别
可以修改 `EntityProtectionManager.ProtectEntity()` 方法来支持不同的保护级别：

```csharp
// 完全保护
EntityProtectionManager.ProtectEntity(entityId, "FULL");

// 只读保护（允许查看，不允许修改）
EntityProtectionManager.ProtectEntity(entityId, "READONLY");

// 部分保护（允许移动，不允许删除）
EntityProtectionManager.ProtectEntity(entityId, "PARTIAL");
```

### 批量操作
```csharp
// 保护所有选定的图元
foreach (ObjectId objId in selectionSet.GetObjectIds())
{
    EntityProtectionManager.ProtectEntity(objId);
}
```

## 注意事项

### 1. 性能考虑
- Reactor会在每次图元修改时触发，可能影响性能
- 建议只对重要图元使用保护功能
- 大量受保护图元可能会降低AutoCAD响应速度

### 2. 兼容性
- 需要AutoCAD 2018或更高版本
- 保护信息存储在DWG文件中，与旧版本AutoCAD兼容
- 在没有插件的环境中，保护功能不会生效

### 3. 安全性
- 这种保护是软件级别的，不是加密保护
- 有经验的用户可能绕过保护机制
- 适用于防止意外修改，不适用于安全敏感的场景

### 4. 故障排除
- 如果保护不生效，检查Reactor是否正确附加
- 使用 `CHECKPROTECTION` 命令验证保护状态
- 查看AutoCAD命令行的错误消息

## 扩展开发

### 添加新的保护类型
1. 修改 `EntityProtectionReactor` 类添加新的事件处理
2. 在扩展字典中存储额外的保护参数
3. 更新命令接口支持新的保护选项

### 集成到现有系统
```csharp
// 在你的代码中使用保护功能
if (needsProtection)
{
    EntityProtectionManager.ProtectEntity(entityId, "CUSTOM_LEVEL");
}

// 检查保护状态
if (EntityProtectionManager.IsEntityProtected(entityId))
{
    // 处理受保护的图元
}
```

## 许可证

此项目仅供学习和参考使用。请根据您的具体需求进行修改和使用。

## 技术支持

如果您在使用过程中遇到问题，请检查：
1. AutoCAD版本兼容性
2. .NET Framework版本
3. 程序集引用路径
4. 命令行错误消息
