using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using System;
using System.Collections.Generic;

namespace EntityProtection
{
    /// <summary>
    /// 命令拦截器 - 实现真正的图元保护
    /// 通过拦截可能修改图元的命令来实现保护
    /// </summary>
    public static class CommandInterceptor
    {
        private static bool _isInitialized = false;
        private static readonly object _lockObject = new object();

        // 需要拦截的命令列表
        private static readonly HashSet<string> _protectedCommands = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "ERASE", "DELETE", "MOVE", "COPY", "ROTATE", "SCALE", "STRETCH", 
            "MIRROR", "ARRAY", "OFFSET", "TRIM", "EXTEND", "BREAK", "FILLET",
            "CHAMFER", "EXPLODE", "PEDIT", "SPLINEDIT", "HATCHEDIT", "DDEDIT",
            "PROPERTIES", "MATCHPROP", "CHPROP", "CHANGE"
        };

        /// <summary>
        /// 初始化命令拦截器
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            lock (_lockObject)
            {
                if (_isInitialized) return;

                try
                {
                    // 订阅文档管理器事件
                    Application.DocumentManager.DocumentActivated += OnDocumentActivated;
                    Application.DocumentManager.DocumentCreated += OnDocumentCreated;

                    // 为当前活动文档订阅事件
                    var activeDoc = Application.DocumentManager.MdiActiveDocument;
                    if (activeDoc != null)
                    {
                        SubscribeToDocumentEvents(activeDoc);
                    }

                    _isInitialized = true;
                    LogMessage("命令拦截器已初始化");
                }
                catch (System.Exception ex)
                {
                    LogMessage($"初始化命令拦截器失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 清理命令拦截器
        /// </summary>
        public static void Cleanup()
        {
            if (!_isInitialized) return;

            lock (_lockObject)
            {
                try
                {
                    Application.DocumentManager.DocumentActivated -= OnDocumentActivated;
                    Application.DocumentManager.DocumentCreated -= OnDocumentCreated;

                    // 取消订阅所有文档的事件
                    foreach (Document doc in Application.DocumentManager)
                    {
                        UnsubscribeFromDocumentEvents(doc);
                    }

                    _isInitialized = false;
                    LogMessage("命令拦截器已清理");
                }
                catch (System.Exception ex)
                {
                    LogMessage($"清理命令拦截器失败: {ex.Message}");
                }
            }
        }

        private static void OnDocumentActivated(object sender, DocumentCollectionEventArgs e)
        {
            if (e.Document != null)
            {
                SubscribeToDocumentEvents(e.Document);
            }
        }

        private static void OnDocumentCreated(object sender, DocumentCollectionEventArgs e)
        {
            if (e.Document != null)
            {
                SubscribeToDocumentEvents(e.Document);
            }
        }

        private static void SubscribeToDocumentEvents(Document doc)
        {
            try
            {
                // 取消之前的订阅（避免重复订阅）
                UnsubscribeFromDocumentEvents(doc);

                // 订阅命令事件
                doc.CommandWillStart += OnCommandWillStart;
                doc.CommandEnded += OnCommandEnded;
                doc.CommandCancelled += OnCommandCancelled;
                doc.CommandFailed += OnCommandFailed;
            }
            catch (System.Exception ex)
            {
                LogMessage($"订阅文档事件失败: {ex.Message}");
            }
        }

        private static void UnsubscribeFromDocumentEvents(Document doc)
        {
            try
            {
                doc.CommandWillStart -= OnCommandWillStart;
                doc.CommandEnded -= OnCommandEnded;
                doc.CommandCancelled -= OnCommandCancelled;
                doc.CommandFailed -= OnCommandFailed;
            }
            catch
            {
                // 忽略取消订阅错误
            }
        }

        private static void OnCommandWillStart(object sender, CommandEventArgs e)
        {
            try
            {
                // 检查是否是需要保护的命令
                if (!_protectedCommands.Contains(e.GlobalCommandName))
                    return;

                // 获取当前选择集
                var doc = sender as Document;
                if (doc?.Editor == null) return;

                var selectionSet = doc.Editor.SelectImplied();
                if (selectionSet.Status != PromptStatus.OK || selectionSet.Value == null)
                    return;

                // 检查选择集中是否有受保护的图元
                var protectedEntities = new List<ObjectId>();
                foreach (ObjectId objId in selectionSet.Value.GetObjectIds())
                {
                    if (EntityProtectionManager.IsEntityProtected(objId))
                    {
                        protectedEntities.Add(objId);
                    }
                }

                if (protectedEntities.Count > 0)
                {
                    // 警告用户但不能直接取消命令
                    LogMessage($"警告：命令 {e.GlobalCommandName} 将影响 {protectedEntities.Count} 个受保护的图元");

                    foreach (var objId in protectedEntities)
                    {
                        var info = EntityProtectionManager.GetProtectionInfo(objId);
                        LogMessage($"  - 受保护图元 {objId.Handle}: {info?.Level}");
                    }

                    // 注意：CommandEventArgs没有Cancel属性
                    // 我们只能记录警告，无法直接取消命令
                    LogMessage("建议：请手动取消当前命令或重新选择不包含受保护图元的对象");
                }
            }
            catch (System.Exception ex)
            {
                LogMessage($"命令拦截处理出错: {ex.Message}");
            }
        }

        private static void OnCommandEnded(object sender, CommandEventArgs e)
        {
            // 命令正常结束，可以在这里添加日志记录
        }

        private static void OnCommandCancelled(object sender, CommandEventArgs e)
        {
            // 命令被取消，可以在这里添加日志记录
        }

        private static void OnCommandFailed(object sender, CommandEventArgs e)
        {
            // 命令失败，可以在这里添加日志记录
        }

        /// <summary>
        /// 添加需要保护的命令
        /// </summary>
        /// <param name="commandName">命令名称</param>
        public static void AddProtectedCommand(string commandName)
        {
            if (!string.IsNullOrEmpty(commandName))
            {
                _protectedCommands.Add(commandName.ToUpper());
            }
        }

        /// <summary>
        /// 移除保护的命令
        /// </summary>
        /// <param name="commandName">命令名称</param>
        public static void RemoveProtectedCommand(string commandName)
        {
            if (!string.IsNullOrEmpty(commandName))
            {
                _protectedCommands.Remove(commandName.ToUpper());
            }
        }

        /// <summary>
        /// 获取所有受保护的命令
        /// </summary>
        /// <returns>受保护的命令列表</returns>
        public static IEnumerable<string> GetProtectedCommands()
        {
            return new List<string>(_protectedCommands);
        }

        private static void LogMessage(string message)
        {
            try
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage($"\n[保护系统] {message}");
            }
            catch
            {
                // 忽略日志错误
            }
        }
    }

    /// <summary>
    /// 命令拦截器初始化类
    /// </summary>
    public class CommandInterceptorInitializer : IExtensionApplication
    {
        public void Initialize()
        {
            CommandInterceptor.Initialize();
        }

        public void Terminate()
        {
            CommandInterceptor.Cleanup();
        }
    }
}
